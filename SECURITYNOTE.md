# MindSpeed-RL 安全声明

## 系统安全加固

- 用户可在运行系统配置时开启 ASLR（级别2）以提高系统安全性，保护系统随机化开启。  
可参考以下方式进行配置：

  ```
  echo 2 > /proc/sys/kernel/randomize_va_space
  ```

## 运行用户建议

- 基于安全性考虑，建议您在执行任何命令时，不建议使用root等管理员类型账户执行，遵循权限最小化原则。

## 文件权限控制
1. 建议用户在主机（包括宿主机）及容器中设置运行系统umask值为0027及以上，保障新增文件夹默认最高权限为750，新增文件默认最高权限为640。
2. 建议用户对个人数据、商业资产、源文件、训练过程中保存的各类文件等敏感内容做好权限管控。涉及场景如MindSpeed-RL-LLM安装目录权限管控、多用户使用共享数据集权限管控，管控权限可参考表1进行设置。
3. MindSpeed-RL-LLM在数据预处理中会生成训练数据，在训练过程会生成权重文件，文件权限默认640，用户可根据实际需求对生成文件权限进行进阶管控。

**表1 文件（夹）各场景权限管控推荐最大值**
| 类型          | linux权限参考最大值 |
| --------------- | --------------------|
| 用户主目录                          |    750（rwxr-x---）                |
| 程序文件（含脚本文件、库文件等）      |    550（r-xr-x---）                |
| 程序文件目录                        |    550（r-xr-x---）                |
| 配置文件                            |    640（rw-r-----）                |
| 配置文件目录                        |    750（rwxr-x---）                |
| 日志文件（记录完毕或者已经归档）      |    440（r--r-----）                |
| 日志文件（正在记录）                 |    640（rw-r-----）                |
| 日志文件记录                        |    750（rwxr-x---）                |
| Debug文件                          |    640（rw-r-----）                |
| Debug文件目录                      |    750 (rwxr-x---)                 |
| 临时文件目录                       |     750（rwxr-x---）                |
| 维护升级文件目录                    |    770（rwxrwx---）                |
| 业务数据文件                       |     640（rw-r-----）                |
| 业务数据文件目录                   |     750（rwxr-x---）                |
| 密钥组件、私钥、证书、密文文件目录   |     700（rwx------）                |
| 密钥组件、私钥、证书、加密密文      |     600（rw-------）                |
| 加解密接口、加解密脚本             |     500（r-x------）                |


## 运行安全声明

- 建议用户结合运行环境资源状况编写对应训练脚本。若训练脚本与资源状况不匹配，如数据集加载内存大小超出内存容量限制、训练脚本在本地生成数据超过磁盘空间大小等情况，可能引发错误并导致进程意外退出。
- MindSpeed-RL 在运行异常时会退出进程并打印报错信息，建议根据报错提示定位具体错误原因，包括设定算子同步执行、查看 CANN 日志、解析生成的 Core Dump 文件等方式。


## 公开接口声明
MindSpeed-RL 暂时未发布wheel包，无正式对外公开接口，所有功能均通过shell脚本调用。入口脚本皆放置于cli目录下，分别为  train_grpo.py, train_orm.py, preprocess_data.py, convert_ckpt.py 和 infer_vllm.py。


## 公网地址声明
- MindSpeed-RL代码中包含公网地址声明如下表所示：

|      类型      |                          文件名                             |             公网IP地址/公网URL地址/域名/邮箱地址             |                   用途说明                    |
| :------------: |:----------------------------------------------------------:| :----------------------------------------------------------: |:-----------------------------------------:|
|  开源引入  |                  MindSpeed-RL/tests/test_tools/dist_test.py       |          https://github.com/microsoft/DeepSpeed/blob/master/tests/unit/common.py      |              开源引入 common 参考代码          |
|  开源引入  |                  MindSpeed-RL/mindspeed_rl/models/rollout/vllm_adapter/vllm_parallel_state.py      |          *******      |              引入公网IP地址          |
|  开源引入  |                  MindSpeed-RL/mindspeed_rl/workers/base_worker.py       |          *******      |              引入公网IP地址          |


## 通信安全加固

[通信安全加固说明](https://gitee.com/ascend/pytorch/blob/master/SECURITYNOTE.md#%E9%80%9A%E4%BF%A1%E5%AE%89%E5%85%A8%E5%8A%A0%E5%9B%BA
)

## 通信矩阵
[通信矩阵说明](https://gitee.com/ascend/pytorch/blob/master/SECURITYNOTE.md#%E9%80%9A%E4%BF%A1%E7%9F%A9%E9%98%B5%E4%BF%A1%E6%81%AF)

### 特殊场景
| 场景                                  | 使用方法                                         | 端口 | 可能的风险       |
| ------------------------------------- | ------------------------------------------------ | ---------- | ---------- |
| 用户下载并使用HuggingFace的开源数据集 | 调用`load_dataset`函数，并填写目标开源数据集路径 | 随机端口     | 数据集可能包含敏感或不合法内容，导致合规问题。数据集中可能存在质量问题，如标签错误或数据偏差，影响数据预处理。   |
| 使用 MindSpeed-RL 进行训练任务时，新增32个端口 | 使用 pytorch 分布式训练拉起任一任务 | [1024,65520]内 | 网络配置错误可能引发端口冲突或连接问题，影响训练效率。   |

