defaults:
  - model:
      - qwen25_7b

megatron_training:
  model: qwen25_7b
  use_fused_rmsnorm: true
  use_mcore_models: true
  sequence_parallel: true
  use_flash_attn: true
  no_masked_softmax_fusion: true
  attention_softmax_in_fp32: true
  no_gradient_accumulation_fusion: true
  use_fused_swiglu: true
  use_fused_rotary_pos_emb: true
  bf16: true
  use_distributed_optimizer: true
  tokenizer_type: PretrainedFromHF
  tokenizer_name_or_path: /data/for_dt/weights/Qwen2.5-7B
  global_batch_size: 8
  seq_length: 2048
  save_interval: 50
  train_iters: 1
  stage: ray_dapo
  attention_dropout: 0.0
  init_method_std: 0.01
  hidden_dropout: 0.0
  distributed_backend: nccl
  no_shared_storage: true
  dataset_additional_keys: ['labels',]
  data_path: /data/for_dt/datasets/pe-nlp/data
  split: 100,0,0
  no_shuffle: true
  full_shuffle_instruction_dataset: false


actor_config:
  model: qwen25_7b
  micro_batch_size: 1   
  tensor_model_parallel_size: 4
  pipeline_model_parallel_size: 1
  lr: 1e-6
  lr_decay_style: constant
  min_lr: 0
  weight_decay: 0.01
  lr_warmup_fraction: 0.0
  clip_grad: 1.0
  adam_beta1: 0.9
  adam_beta2: 0.95
  finetune: true
  load: /data/for_dt/weights/Qwen2.5-7B-tp4
  save: ./ckpt
  no_load_optim: true
  no_load_rng: true
  no_save_optim: false
  no_save_rng: false

rl_config:
  guarantee_order: false
  use_integrated_worker: true
  blocking: true
  actor_forward_micro_batch_size: 1
  ref_forward_micro_batch_size: 1
  gamma: 1.0
  lam: 0.95
  adv_estimator: group_norm
  mini_batch_size: 32    
  max_prompt_length: 2048
  epochs: 1
  clip_ratio: 0.2
  entropy_coeff: 0.0
  shuffle_mini_batch: false
  n_samples_per_prompt: 8
  rule_reward: true
  verifier_function: ["math_17k_acc"]
  verifier_weight: [1.0]
  num_cpus_for_local_task: 1.0
  use_tensorboard: true

  #token level loss策略
  token_level_loss: true

  #clip Higher策略
  clip_higher_enable: true
  clip_ratio_low: 0.2
  clip_ratio_high: 0.28

  #过长response惩罚措施
  overlong_buffer_enable: true
  rollout_max_tokens : 2048
  overlong_buffer: 512
  overlong_buffer_penalty_factor: 1.0

  #Prompt过滤措施
  filter_groups_enable: true
  filter_groups_metric: grpo/math_17k_acc_rewards/mean
  filter_groups_max_batches: -1
  filter_groups_train_batch_size: 2

  actor_resource:
    num_npus: 8

generate_config:
  trust_remote_code: true
  offload_train_optimizer: true
  offload_train_grad: true
  offload_train_param: true

  # 推理时的并行配置
  infer_tensor_parallel_size: 4
  infer_pipeline_parallel_size: 1
  infer_expert_parallel_size: 1

  # vllm 模型相关设置
  max_num_seqs: 1024
  max_model_len: 4096
  max_num_batched_tokens: 8192
  dtype: "bfloat16"
  gpu_memory_utilization: 0.4
#  num_scheduler_steps: 1

  # 采样配置
  sampling_config:
    logprobs: 1
    max_tokens: 2048
    top_p: 1.0
    top_k: -1
    min_p: 0.0
    # seed: 1234
    temperature: 1.0
    detokenize: false

profiler_config:
  integrated:
    profile: true
    mstx: false
    stage: actor_update
    profile_save_path: ./profiler_data
    profile_export_type: db
    profile_step_start: 1
    profile_step_end: 2
    profile_level: level1
    profile_with_memory: false
    profile_record_shapes: false
    profile_with_cpu: true
    profile_with_npu: true
    profile_with_module: false
    profile_analysis: true
    profile_ranks: [0, 1]

msprobe_config:
  msprobe: true
  dump_path: "./msprobe_dump"
  key_data_dump: true
  configurations_dump: true
  actor_train_dump: true
  reference_dump: true
  step_start: 0
  step_end: 0