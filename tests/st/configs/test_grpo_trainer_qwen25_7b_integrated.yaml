defaults:
  - model:
      - qwen25_7b

megatron_training:
  model: qwen25_7b
  use_fused_rmsnorm: true
  use_mcore_models: true
  sequence_parallel: true
  use_flash_attn: true
  reset_position_ids: true
  no_masked_softmax_fusion: true
  attention_softmax_in_fp32: true
  no_gradient_accumulation_fusion: true
  use_fused_swiglu: true
  use_fused_rotary_pos_emb: true
  use_ascend_coc: True
  coc_fused_kernel: True
  coc_parallel_num: 2
  bf16: true
  use_distributed_optimizer: true
  tokenizer_type: PretrainedFromHF
  tokenizer_name_or_path: /data/for_dt/weights/Qwen2.5-7B
  global_batch_size: 4
  seq_length: 1024
  save_interval: 50
  train_iters: 1
  stage: ray_grpo
  attention_dropout: 0.0
  init_method_std: 0.01
  hidden_dropout: 0.0
  distributed_backend: nccl
  no_shared_storage: true
  variable_seq_lengths: true
  dataset_additional_keys: ['labels',]
  data_path: /data/for_dt/datasets/pe-nlp/data
  split: 100,0,0

actor_config:
  model: qwen25_7b
  micro_batch_size: 1
  tensor_model_parallel_size: 4
  pipeline_model_parallel_size: 1
  lr: 5e-7
  lr_decay_style: cosine
  min_lr: 5e-8
  weight_decay: 0.0
  lr_warmup_fraction: 0.0
  clip_grad: 10000.0
  adam_beta1: 0.9
  adam_beta2: 0.95
  initial_loss_scale: 4096
  finetune: true
  load: /data/for_dt/weights/Qwen2.5-7B-tp4
  save: ./ckpt
  no_load_optim: true
  no_load_rng: true

rl_config:
  use_integrated_worker: true
  blocking: true
  use_dp_batch_balance: true
  use_dynamic_bsz: true
  max_packing_token_size: 8192
  actor_forward_micro_batch_size: 8
  ref_forward_micro_batch_size: 8
  use_remove_padding: true
  gamma: 1.0
  lam: 0.95
  adv_estimator: group_norm
  kl_penalty: kl
  kl_ctrl_type: fixed
  init_kl_coef: 0.0001
  mini_batch_size: 64
  max_prompt_length: 1024
  epochs: 1
  clip_ratio: 0.2
  entropy_coeff: 0.001
  n_samples_per_prompt: 8
  rule_reward: true
  verifier_function: ["math_17k_acc"]
  verifier_weight: [1.0]
  verifier_parallel: 4
  verifier_timeout: 120
  use_tensorboard: true
  actor_resource:
    num_npus: 8

generate_config:
  trust_remote_code: true
  offload_train_optimizer: true
  offload_train_grad: true
  offload_train_param: true

  # 推理时的并行配置
  infer_tensor_parallel_size: 2
  infer_pipeline_parallel_size: 1
  infer_expert_parallel_size: 1

  # vllm 模型相关设置
  max_num_seqs: 512
  max_model_len: 4096
  dtype: "bfloat16"
  gpu_memory_utilization: 0.9
  num_scheduler_steps: 1

  # 采样配置
  sampling_config:
    logprobs: 1
    max_tokens: 64
    top_p: 0.9
    top_k: 50
    min_p: 0.01
    temperature: 0.8
    detokenize: false

profiler_config:
  integrated:
    profile: true
    mstx: false
    stage: actor_update
    profile_save_path: ./profiler_data
    profile_export_type: db
    profile_step_start: 1
    profile_step_end: 2
    profile_level: level1
    profile_with_memory: false
    profile_record_shapes: false
    profile_with_cpu: true
    profile_with_npu: true
    profile_with_module: false
    profile_analysis: true
    profile_ranks: [0, 1]

msprobe_config:
  msprobe: true
  dump_path: "./msprobe_dump"
  key_data_dump: true
  configurations_dump: true
  actor_train_dump: true
  reference_dump: true
  step_start: 0
  step_end: 0